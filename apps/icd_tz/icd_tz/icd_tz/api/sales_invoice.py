import frappe
from icd_tz.icd_tz.api.utils import validate_qty_storage_item


def before_save(doc, method):
    validate_qty_storage_item(doc)


def on_submit(doc, method):
    update_sales_references(doc)


def update_sales_references(doc):
    """
    Update container references when a sales invoice is submitted.
    Enhanced version with better error handling and logging.
    """

    # Log the start of the process
    frappe.logger().info(f"Starting sales reference update for invoice {doc.name}")

    if not doc.m_bl_no:
        frappe.logger().warning(f"Invoice {doc.name} has no M BL No - skipping reference update")
        return

    invoice_id = doc.name
    if doc.is_return:
        invoice_id = None

    try:
        settings_doc = frappe.get_cached_doc("ICD TZ Settings")
    except Exception as e:
        frappe.logger().error(f"Could not load ICD TZ Settings: {str(e)}")
        return

    # Build service mappings with error handling
    corridor_services = []
    verification_services = []
    stripping_services = []
    removal_services = []
    transport_services = []
    storage_services = []
    shore_services = []

    try:
        corridor_services = [row.service_name for row in settings_doc.service_types if row.service_type == "Levy"]
        verification_services = [row.service_name for row in settings_doc.service_types if row.service_type == "Verification"]
        stripping_services = [row.service_name for row in settings_doc.service_types if row.service_type == "Stripping"]
        removal_services = [row.service_name for row in settings_doc.service_types if row.service_type == "Removal"]
        transport_services = [row.service_name for row in settings_doc.service_types if row.service_type == "Transport"]
        storage_services = [row.service_name for row in settings_doc.service_types if row.service_type in ["Storage-Single", "Storage-Double"]]
        shore_services = [row.service_name for row in settings_doc.service_types if row.service_type == "Shore"]

        # for loose container
        corridor_services += [row.service_name for row in settings_doc.loose_types if row.service_type == "Levy"]
        verification_services += [row.service_name for row in settings_doc.loose_types if row.service_type == "Verification"]
        stripping_services += [row.service_name for row in settings_doc.loose_types if row.service_type == "Stripping"]
        removal_services += [row.service_name for row in settings_doc.loose_types if row.service_type == "Removal"]
        transport_services += [row.service_name for row in settings_doc.loose_types if row.service_type == "Transport"]
        storage_services += [row.service_name for row in settings_doc.loose_types if row.service_type in ["Storage-Single", "Storage-Double"]]
        shore_services += [row.service_name for row in settings_doc.loose_types if row.service_type == "Shore"]
    except Exception as e:
        frappe.logger().error(f"Error building service mappings: {str(e)}")
        return

    # Process each item with enhanced error handling
    for item in doc.items:
        try:
            # Check if container_id is present
            if not item.container_id:
                frappe.logger().warning(f"Invoice {doc.name}, Item {item.item_code}: No container_id found")
                # Try to find container by container_no and m_bl_no
                if hasattr(item, 'container_no') and item.container_no:
                    container_id = frappe.db.get_value("Container", {"container_no": item.container_no, "m_bl_no": doc.m_bl_no}, "name")
                    if container_id:
                        frappe.logger().info(f"Found container {container_id} for item {item.item_code} using container_no")
                        item.container_id = container_id
                    else:
                        frappe.logger().warning(f"Could not find container for item {item.item_code} with container_no {item.container_no}")
                        continue
                else:
                    frappe.logger().warning(f"Item {item.item_code} has no container_id or container_no - skipping")
                    continue

            # Process the item based on service type
            if item.item_code in transport_services:
                update_container_reception(item.container_id, invoice_id, "t_sales_invoice")
                frappe.logger().info(f"Updated transport charges for container {item.container_id}")

            elif item.item_code in shore_services:
                update_container_reception(item.container_id, invoice_id, "s_sales_invoice")
                frappe.logger().info(f"Updated shore handling charges for container {item.container_id}")

            elif item.item_code in stripping_services:
                update_booking_refs(item.container_id, invoice_id, "s_sales_invoice")
                frappe.logger().info(f"Updated stripping charges for container {item.container_id}")

            elif item.item_code in verification_services:
                update_booking_refs(item.container_id, invoice_id, "cv_sales_invoice")
                frappe.logger().info(f"Updated verification charges for container {item.container_id}")

            elif item.item_code in removal_services:
                update_container_refs(item.container_id, invoice_id, "r_sales_invoice")
                frappe.logger().info(f"Updated removal charges for container {item.container_id}")

            elif item.item_code in corridor_services:
                update_container_refs(item.container_id, invoice_id, "c_sales_invoice")
                frappe.logger().info(f"Updated corridor levy charges for container {item.container_id}")

            elif item.item_code in storage_services:
                update_storage_date_refs(item.container_id, invoice_id, item.container_child_refs)
                frappe.logger().info(f"Updated storage charges for container {item.container_id}")

            else:
                update_container_insp(item.container_id, item.item_code, invoice_id)
                frappe.logger().info(f"Updated inspection charges for container {item.container_id}, service {item.item_code}")

        except Exception as e:
            frappe.logger().error(f"Error processing item {item.item_code} in invoice {doc.name}: {str(e)}")
            # Continue processing other items instead of failing completely
            continue
    
    sales_order = doc.items[0].sales_order
    service_orders = frappe.db.get_all(
        "Service Order",
        filters={"sales_order": sales_order},
    )
    for row in service_orders:
        frappe.db.set_value(
            "Service Order",
            row.name,
            "sales_invoice",
            invoice_id
        )


def update_container_reception(container_id, invoice_id, field):
    """Update container reception with invoice reference"""
    try:
        container_reception = frappe.db.get_value(
            "Container",
            container_id,
            "container_reception"
        )

        if container_reception:
            frappe.db.set_value(
                "Container Reception",
                container_reception,
                field,
                invoice_id
            )
            frappe.logger().info(f"Updated {field} = {invoice_id} for Container Reception {container_reception}")
        else:
            frappe.logger().warning(f"No Container Reception found for container {container_id}")
    except Exception as e:
        frappe.logger().error(f"Error updating container reception for {container_id}: {str(e)}")


def update_booking_refs(container_id, invoice_id, field):
    """Update in yard container booking with invoice reference"""
    try:
        filters = {
            "container_id": container_id,
            "docstatus": 1
        }

        booking_ids = frappe.db.get_all("In Yard Container Booking", filters, pluck="name")
        if len(booking_ids) == 0:
            frappe.logger().warning(f"No submitted In Yard Container Booking found for container {container_id}")
            return

        for booking_id in booking_ids:
            frappe.db.set_value(
                "In Yard Container Booking",
                booking_id,
                field,
                invoice_id
            )
            frappe.logger().info(f"Updated {field} = {invoice_id} for In Yard Container Booking {booking_id}")
    except Exception as e:
        frappe.logger().error(f"Error updating booking refs for {container_id}: {str(e)}")


def update_container_refs(container_id, invoice_id, field):
    """Update container document with invoice reference"""
    try:
        container_doc = frappe.get_doc("Container", container_id)
        container_doc.update({
            field: invoice_id
        })
        container_doc.status = "At Gatepass"
        container_doc.save(ignore_permissions=True)
        frappe.logger().info(f"Updated {field} = {invoice_id} for Container {container_id}")
    except Exception as e:
        frappe.logger().error(f"Error updating container refs for {container_id}: {str(e)}")


def update_storage_date_refs(container_id, invoice_id, child_refs):
    container_doc = frappe.get_doc("Container", container_id)
    for child in container_doc.container_dates:
        if child.name in child_refs:
            child.sales_invoice = invoice_id
    
    container_doc.status = "At Gatepass"
    container_doc.save(ignore_permissions=True)


def update_container_insp(container_id, item_code, invoice_id):
    container_inspections = frappe.db.get_all("Container Inspection", {"container_id": container_id, "docstatus": 1}, pluck="name")
    if len(container_inspections) == 0:
        return
    
    for inspeaction in container_inspections:
        insp_doc = frappe.get_doc("Container Inspection", inspeaction)
        for row in insp_doc.services:
            if row.service == item_code:
                frappe.db.set_value(
                    "Container Inspection Detail",
                    row.name,
                    "sales_invoice",
                    invoice_id
                )

