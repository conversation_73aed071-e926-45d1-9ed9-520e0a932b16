import frappe
from frappe.utils import get_url_to_form


@frappe.whitelist()
def diagnose_container_invoice_linking(container_id):
    """
    Diagnostic tool to identify why container invoices are not being linked properly
    """
    
    if not container_id:
        return {"error": "Container ID is required"}
    
    # Check if container exists
    if not frappe.db.exists("Container", container_id):
        return {"error": f"Container {container_id} does not exist"}
    
    container_doc = frappe.get_doc("Container", container_id)
    
    # Get container details
    result = {
        "container_id": container_id,
        "container_no": container_doc.container_no,
        "m_bl_no": container_doc.m_bl_no,
        "status": container_doc.status,
        "issues": [],
        "invoice_references": {},
        "related_invoices": [],
        "service_mapping": {},
        "recommendations": []
    }
    
    # Check current invoice references
    result["invoice_references"] = {
        "r_sales_invoice": container_doc.r_sales_invoice,
        "c_sales_invoice": container_doc.c_sales_invoice,
        "has_removal_charges": container_doc.has_removal_charges,
        "has_corridor_levy_charges": container_doc.has_corridor_levy_charges
    }
    
    # Find all sales invoices that might be related to this container
    if container_doc.m_bl_no:
        invoices = frappe.db.get_all(
            "Sales Invoice",
            filters={"m_bl_no": container_doc.m_bl_no, "docstatus": 1},
            fields=["name", "posting_date", "grand_total", "status"]
        )
        
        for invoice in invoices:
            invoice_items = frappe.db.get_all(
                "Sales Invoice Item",
                filters={"parent": invoice.name},
                fields=["item_code", "container_id", "container_no", "amount"]
            )
            
            result["related_invoices"].append({
                "invoice_id": invoice.name,
                "posting_date": invoice.posting_date,
                "grand_total": invoice.grand_total,
                "status": invoice.status,
                "items": invoice_items,
                "url": get_url_to_form("Sales Invoice", invoice.name)
            })
    
    # Get ICD TZ Settings service mapping
    try:
        settings_doc = frappe.get_cached_doc("ICD TZ Settings")
        service_types = {}
        
        for row in settings_doc.service_types:
            if row.service_type not in service_types:
                service_types[row.service_type] = []
            service_types[row.service_type].append(row.service_name)
        
        for row in settings_doc.loose_types:
            if row.service_type not in service_types:
                service_types[row.service_type] = []
            service_types[row.service_type].append(row.service_name)
        
        result["service_mapping"] = service_types
        
    except Exception as e:
        result["issues"].append(f"Could not load ICD TZ Settings: {str(e)}")
    
    # Check for common issues
    if not container_doc.m_bl_no:
        result["issues"].append("Container has no M BL No - invoices won't be linked automatically")
        result["recommendations"].append("Ensure M BL No is set on the container")
    
    # Check if there are invoices with missing container_id
    if container_doc.m_bl_no:
        invoices_missing_container_id = frappe.db.sql("""
            SELECT si.name, sii.item_code, sii.container_id, sii.container_no
            FROM `tabSales Invoice` si
            JOIN `tabSales Invoice Item` sii ON si.name = sii.parent
            WHERE si.m_bl_no = %s 
            AND si.docstatus = 1
            AND (sii.container_id IS NULL OR sii.container_id = '')
        """, (container_doc.m_bl_no,), as_dict=True)
        
        if invoices_missing_container_id:
            result["issues"].append("Found submitted invoices with missing container_id in items")
            result["recommendations"].append("Update Sales Invoice Items to include container_id field")
            for item in invoices_missing_container_id:
                result["issues"].append(f"Invoice {item.name}, Item: {item.item_code} has no container_id")
    
    # Check if service items are properly mapped
    for invoice in result["related_invoices"]:
        for item in invoice["items"]:
            if item["container_id"] == container_id:
                service_found = False
                for service_type, services in result["service_mapping"].items():
                    if item["item_code"] in services:
                        service_found = True
                        break
                
                if not service_found:
                    result["issues"].append(f"Item {item['item_code']} in invoice {invoice['invoice_id']} is not mapped in ICD TZ Settings")
                    result["recommendations"].append(f"Add {item['item_code']} to appropriate service type in ICD TZ Settings")
    
    # Check container reception
    if container_doc.container_reception:
        reception_doc = frappe.get_doc("Container Reception", container_doc.container_reception)
        result["container_reception"] = {
            "name": reception_doc.name,
            "has_transport_charges": reception_doc.has_transport_charges,
            "t_sales_invoice": reception_doc.t_sales_invoice,
            "has_shore_handling_charges": reception_doc.has_shore_handling_charges,
            "s_sales_invoice": reception_doc.s_sales_invoice
        }
    else:
        result["issues"].append("Container has no linked Container Reception")
    
    # Check bookings
    bookings = frappe.db.get_all(
        "In Yard Container Booking",
        filters={"container_id": container_id, "docstatus": 1},
        fields=["name", "has_stripping_charges", "s_sales_invoice", "has_custom_verification_charges", "cv_sales_invoice"]
    )
    
    result["bookings"] = bookings
    if not bookings:
        result["issues"].append("Container has no submitted In Yard Container Booking")
    
    # Check inspections
    inspections = frappe.db.get_all(
        "Container Inspection",
        filters={"container_id": container_id, "docstatus": 1},
        fields=["name"]
    )
    
    result["inspections"] = []
    for inspection in inspections:
        inspection_doc = frappe.get_doc("Container Inspection", inspection.name)
        services = []
        for service in inspection_doc.services:
            services.append({
                "service": service.service,
                "sales_invoice": service.sales_invoice
            })
        result["inspections"].append({
            "name": inspection.name,
            "services": services
        })
    
    return result


@frappe.whitelist()
def fix_missing_container_references(container_id):
    """
    Attempt to fix missing container invoice references by re-running the linking logic
    """

    if not container_id:
        return {"error": "Container ID is required"}

    container_doc = frappe.get_doc("Container", container_id)

    if not container_doc.m_bl_no:
        return {"error": "Container has no M BL No - cannot link invoices"}

    # Find all submitted invoices for this M BL No
    invoices = frappe.db.get_all(
        "Sales Invoice",
        filters={"m_bl_no": container_doc.m_bl_no, "docstatus": 1},
        fields=["name"]
    )

    fixed_count = 0
    errors = []

    for invoice in invoices:
        try:
            # Re-run the linking logic for this invoice
            invoice_doc = frappe.get_doc("Sales Invoice", invoice.name)

            # Import the update function
            from icd_tz.icd_tz.api.sales_invoice import update_sales_references
            update_sales_references(invoice_doc)

            fixed_count += 1

        except Exception as e:
            errors.append(f"Error processing invoice {invoice.name}: {str(e)}")

    return {
        "success": True,
        "fixed_invoices": fixed_count,
        "errors": errors
    }


@frappe.whitelist()
def bulk_fix_container_references(m_bl_no=None, date_from=None, date_to=None):
    """
    Bulk fix missing container invoice references for multiple containers
    """

    filters = {"docstatus": 1}

    if m_bl_no:
        filters["m_bl_no"] = m_bl_no

    if date_from:
        filters["posting_date"] = [">=", date_from]

    if date_to:
        if "posting_date" in filters:
            filters["posting_date"] = ["between", [date_from, date_to]]
        else:
            filters["posting_date"] = ["<=", date_to]

    # Find all submitted invoices matching criteria
    invoices = frappe.db.get_all(
        "Sales Invoice",
        filters=filters,
        fields=["name", "m_bl_no", "posting_date"]
    )

    fixed_count = 0
    errors = []
    processed_invoices = []

    for invoice in invoices:
        try:
            # Re-run the linking logic for this invoice
            invoice_doc = frappe.get_doc("Sales Invoice", invoice.name)

            # Import the update function
            from icd_tz.icd_tz.api.sales_invoice import update_sales_references
            update_sales_references(invoice_doc)

            fixed_count += 1
            processed_invoices.append({
                "invoice": invoice.name,
                "m_bl_no": invoice.m_bl_no,
                "posting_date": invoice.posting_date,
                "status": "Fixed"
            })

        except Exception as e:
            error_msg = f"Error processing invoice {invoice.name}: {str(e)}"
            errors.append(error_msg)
            processed_invoices.append({
                "invoice": invoice.name,
                "m_bl_no": invoice.m_bl_no,
                "posting_date": invoice.posting_date,
                "status": f"Error: {str(e)}"
            })

    return {
        "success": True,
        "total_invoices": len(invoices),
        "fixed_invoices": fixed_count,
        "errors": errors,
        "processed_invoices": processed_invoices
    }
