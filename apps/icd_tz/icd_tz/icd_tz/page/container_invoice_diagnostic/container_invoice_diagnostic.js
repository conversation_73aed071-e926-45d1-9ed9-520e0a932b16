frappe.pages['container-invoice-diagnostic'].on_page_load = function(wrapper) {
	var page = frappe.ui.make_app_page({
		parent: wrapper,
		title: 'Container Invoice Diagnostic',
		single_column: true
	});

	frappe.container_diagnostic = new ContainerInvoiceDiagnostic(page);
}

class ContainerInvoiceDiagnostic {
	constructor(page) {
		this.page = page;
		this.make();
	}

	make() {
		this.page.main.html(`
			<div class="container-diagnostic">
				<div class="row">
					<div class="col-md-12">
						<h3>Container Invoice Diagnostic Tool</h3>
						<p class="text-muted">This tool helps diagnose and fix issues with container invoice linking.</p>
					</div>
				</div>
				
				<div class="row">
					<div class="col-md-6">
						<div class="card">
							<div class="card-header">
								<h5>Single Container Diagnosis</h5>
							</div>
							<div class="card-body">
								<div class="form-group">
									<label>Container ID</label>
									<input type="text" class="form-control" id="container_id" placeholder="Enter Container ID">
								</div>
								<button class="btn btn-primary" id="diagnose_btn">Diagnose Container</button>
								<button class="btn btn-success" id="fix_btn" style="display:none;">Fix Issues</button>
							</div>
						</div>
					</div>
					
					<div class="col-md-6">
						<div class="card">
							<div class="card-header">
								<h5>Bulk Fix</h5>
							</div>
							<div class="card-body">
								<div class="form-group">
									<label>M BL No (Optional)</label>
									<input type="text" class="form-control" id="m_bl_no" placeholder="Enter M BL No">
								</div>
								<div class="form-group">
									<label>Date From (Optional)</label>
									<input type="date" class="form-control" id="date_from">
								</div>
								<div class="form-group">
									<label>Date To (Optional)</label>
									<input type="date" class="form-control" id="date_to">
								</div>
								<button class="btn btn-warning" id="bulk_fix_btn">Bulk Fix Invoices</button>
							</div>
						</div>
					</div>
				</div>
				
				<div class="row mt-4">
					<div class="col-md-12">
						<div id="results_area" style="display:none;">
							<h4>Results</h4>
							<div id="results_content"></div>
						</div>
					</div>
				</div>
			</div>
		`);

		this.bind_events();
	}

	bind_events() {
		const me = this;

		// Diagnose single container
		this.page.main.find('#diagnose_btn').on('click', function() {
			const container_id = me.page.main.find('#container_id').val();
			if (!container_id) {
				frappe.msgprint('Please enter a Container ID');
				return;
			}
			me.diagnose_container(container_id);
		});

		// Fix single container
		this.page.main.find('#fix_btn').on('click', function() {
			const container_id = me.page.main.find('#container_id').val();
			if (!container_id) {
				frappe.msgprint('Please enter a Container ID');
				return;
			}
			me.fix_container(container_id);
		});

		// Bulk fix
		this.page.main.find('#bulk_fix_btn').on('click', function() {
			me.bulk_fix();
		});
	}

	diagnose_container(container_id) {
		const me = this;
		frappe.call({
			method: 'icd_tz.icd_tz.api.container_invoice_diagnostic.diagnose_container_invoice_linking',
			args: { container_id: container_id },
			callback: function(r) {
				if (r.message) {
					me.show_diagnosis_results(r.message);
				}
			}
		});
	}

	fix_container(container_id) {
		const me = this;
		frappe.call({
			method: 'icd_tz.icd_tz.api.container_invoice_diagnostic.fix_missing_container_references',
			args: { container_id: container_id },
			callback: function(r) {
				if (r.message) {
					me.show_fix_results(r.message);
				}
			}
		});
	}

	bulk_fix() {
		const me = this;
		const m_bl_no = me.page.main.find('#m_bl_no').val();
		const date_from = me.page.main.find('#date_from').val();
		const date_to = me.page.main.find('#date_to').val();

		frappe.call({
			method: 'icd_tz.icd_tz.api.container_invoice_diagnostic.bulk_fix_container_references',
			args: { 
				m_bl_no: m_bl_no || null,
				date_from: date_from || null,
				date_to: date_to || null
			},
			callback: function(r) {
				if (r.message) {
					me.show_bulk_fix_results(r.message);
				}
			}
		});
	}

	show_diagnosis_results(data) {
		let html = '<div class="alert alert-info"><h5>Diagnosis Results</h5>';
		
		if (data.error) {
			html += `<div class="alert alert-danger">${data.error}</div>`;
		} else {
			html += `<p><strong>Container:</strong> ${data.container_id} (${data.container_no})</p>`;
			html += `<p><strong>M BL No:</strong> ${data.m_bl_no || 'Not set'}</p>`;
			html += `<p><strong>Status:</strong> ${data.status}</p>`;
			
			if (data.issues.length > 0) {
				html += '<h6>Issues Found:</h6><ul>';
				data.issues.forEach(issue => {
					html += `<li class="text-danger">${issue}</li>`;
				});
				html += '</ul>';
				
				if (data.recommendations.length > 0) {
					html += '<h6>Recommendations:</h6><ul>';
					data.recommendations.forEach(rec => {
						html += `<li class="text-warning">${rec}</li>`;
					});
					html += '</ul>';
				}
				
				this.page.main.find('#fix_btn').show();
			} else {
				html += '<div class="alert alert-success">No issues found!</div>';
				this.page.main.find('#fix_btn').hide();
			}
			
			if (data.related_invoices.length > 0) {
				html += '<h6>Related Invoices:</h6>';
				data.related_invoices.forEach(invoice => {
					html += `<p><a href="${invoice.url}" target="_blank">${invoice.invoice_id}</a> - ${invoice.status} (${invoice.grand_total})</p>`;
				});
			}
		}
		
		html += '</div>';
		
		this.page.main.find('#results_area').show();
		this.page.main.find('#results_content').html(html);
	}

	show_fix_results(data) {
		let html = '<div class="alert alert-success"><h5>Fix Results</h5>';
		
		if (data.error) {
			html += `<div class="alert alert-danger">${data.error}</div>`;
		} else {
			html += `<p>Fixed ${data.fixed_invoices} invoices successfully.</p>`;
			
			if (data.errors.length > 0) {
				html += '<h6>Errors:</h6><ul>';
				data.errors.forEach(error => {
					html += `<li class="text-danger">${error}</li>`;
				});
				html += '</ul>';
			}
		}
		
		html += '</div>';
		
		this.page.main.find('#results_area').show();
		this.page.main.find('#results_content').html(html);
	}

	show_bulk_fix_results(data) {
		let html = '<div class="alert alert-info"><h5>Bulk Fix Results</h5>';
		
		html += `<p>Processed ${data.total_invoices} invoices, fixed ${data.fixed_invoices} successfully.</p>`;
		
		if (data.errors.length > 0) {
			html += '<h6>Errors:</h6><ul>';
			data.errors.forEach(error => {
				html += `<li class="text-danger">${error}</li>`;
			});
			html += '</ul>';
		}
		
		html += '</div>';
		
		this.page.main.find('#results_area').show();
		this.page.main.find('#results_content').html(html);
	}
}
